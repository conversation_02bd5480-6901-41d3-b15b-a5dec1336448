* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    direction: rtl;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    padding: 1rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.logo i {
    font-size: 1.5rem;
    color: #fff;
}

.logo h1 {
    font-size: 1.2rem;
    font-weight: 600;
}

.status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #4ade80;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Chat Container */
.chat-container {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.chat-messages::-webkit-scrollbar {
    width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.chat-messages::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

/* Message Styles */
.message {
    display: flex;
    gap: 0.75rem;
    max-width: 80%;
    animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.user-message {
    align-self: flex-start;
    flex-direction: row-reverse;
}

.ai-message {
    align-self: flex-end;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.user-message .message-avatar {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.ai-message .message-avatar {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
}

.message-content {
    flex: 1;
}

.message-text {
    background: #f8fafc;
    padding: 0.75rem 1rem;
    border-radius: 18px;
    line-height: 1.5;
    word-wrap: break-word;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.user-message .message-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.ai-message .message-text {
    background: #f8fafc;
    color: #1f2937;
}

.message-time {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
    text-align: left;
}

.user-message .message-time {
    text-align: right;
}

/* Typing Indicator */
.typing-dots {
    display: flex;
    gap: 4px;
    padding: 0.75rem 1rem;
    background: #f8fafc;
    border-radius: 18px;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #6b7280;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% {
        transform: scale(0.8);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Suggestions Styles */
.suggestions-container {
    padding: 0.75rem 1rem;
    background: #f8fafc;
    border-top: 1px solid #e5e7eb;
    max-height: 120px;
    overflow-y: auto;
}

.suggestions-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: #4b5563;
    margin-bottom: 0.5rem;
}

.suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.suggestion-item {
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 20px;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s;
    user-select: none;
}

.suggestion-item:hover {
    background: #4facfe;
    color: white;
    border-color: #4facfe;
    transform: translateY(-1px);
}

/* Input Styles */
.chat-input-container {
    padding: 1rem;
    background: white;
    border-top: 1px solid #e5e7eb;
}

.input-wrapper {
    display: flex;
    gap: 0.5rem;
    align-items: flex-end;
    background: #f9fafb;
    border: 2px solid #e5e7eb;
    border-radius: 25px;
    padding: 0.5rem;
    transition: border-color 0.2s;
}

.input-wrapper:focus-within {
    border-color: #4facfe;
}

#messageInput {
    flex: 1;
    border: none;
    outline: none;
    background: transparent;
    resize: none;
    font-family: inherit;
    font-size: 1rem;
    line-height: 1.5;
    max-height: 120px;
    padding: 0.5rem;
}

.send-button {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 50%;
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.2s, box-shadow 0.2s;
}

.send-button:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 12px rgba(79, 172, 254, 0.4);
}

.send-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.input-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 0.5rem;
    font-size: 0.875rem;
}

.action-button {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    transition: background-color 0.2s;
}

.action-button:hover {
    background: #f3f4f6;
    color: #374151;
}

#openChatGPT {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border-radius: 8px;
    font-weight: 500;
}

#openChatGPT:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

#showStats {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    color: white;
    border-radius: 8px;
    font-weight: 500;
}

#showStats:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.character-count {
    color: #6b7280;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        height: 100vh;
        border-radius: 0;
    }
    
    .message {
        max-width: 90%;
    }
    
    .logo h1 {
        font-size: 1rem;
    }
    
    .header {
        padding: 0.75rem;
    }
    
    .chat-messages {
        padding: 0.75rem;
    }
    
    .chat-input-container {
        padding: 0.75rem;
    }
}
