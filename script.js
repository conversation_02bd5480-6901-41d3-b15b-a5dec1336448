class ChatBot {
    constructor() {
        this.chatMessages = document.getElementById('chatMessages');
        this.messageInput = document.getElementById('messageInput');
        this.chatForm = document.getElementById('chatForm');
        this.sendButton = document.getElementById('sendButton');
        this.clearButton = document.getElementById('clearChat');
        this.openChatGPTButton = document.getElementById('openChatGPT');
        this.showStatsButton = document.getElementById('showStats');
        this.typingIndicator = document.getElementById('typingIndicator');
        this.charCount = document.getElementById('charCount');
        this.suggestionsContainer = document.getElementById('suggestionsContainer');
        this.suggestionsList = document.getElementById('suggestionsList');

        // ذاكرة المحادثة
        this.conversationHistory = [];
        this.userPreferences = this.loadUserPreferences();
        this.sessionStartTime = new Date();

        this.init();
    }

    init() {
        this.chatForm.addEventListener('submit', (e) => this.handleSubmit(e));
        this.messageInput.addEventListener('input', () => this.handleInput());
        this.messageInput.addEventListener('keydown', (e) => this.handleKeydown(e));
        this.clearButton.addEventListener('click', () => this.clearChat());
        this.openChatGPTButton.addEventListener('click', () => this.openChatGPT());
        this.showStatsButton.addEventListener('click', () => this.showStatistics());

        // Auto-resize textarea
        this.messageInput.addEventListener('input', () => this.autoResize());

        // إظهار الاقتراحات عند التركيز
        this.messageInput.addEventListener('focus', () => this.showSuggestions());
        this.messageInput.addEventListener('blur', () => {
            // إخفاء الاقتراحات بعد تأخير قصير للسماح بالنقر
            setTimeout(() => this.hideSuggestions(), 200);
        });

        // إظهار اقتراحات ترحيبية
        this.showInitialSuggestions();
    }

    handleSubmit(e) {
        e.preventDefault();
        const message = this.messageInput.value.trim();
        
        if (message) {
            this.sendMessage(message);
            this.messageInput.value = '';
            this.updateCharCount();
            this.autoResize();
        }
    }

    handleInput() {
        this.updateCharCount();
        this.updateSendButton();
    }

    handleKeydown(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            this.chatForm.dispatchEvent(new Event('submit'));
        }
    }

    updateCharCount() {
        const count = this.messageInput.value.length;
        this.charCount.textContent = count;
        
        if (count > 800) {
            this.charCount.style.color = '#ef4444';
        } else if (count > 600) {
            this.charCount.style.color = '#f59e0b';
        } else {
            this.charCount.style.color = '#6b7280';
        }
    }

    updateSendButton() {
        const hasText = this.messageInput.value.trim().length > 0;
        this.sendButton.disabled = !hasText;
    }

    autoResize() {
        this.messageInput.style.height = 'auto';
        this.messageInput.style.height = Math.min(this.messageInput.scrollHeight, 120) + 'px';
    }

    async sendMessage(message) {
        // حفظ الرسالة في التاريخ
        this.conversationHistory.push({
            type: 'user',
            message: message,
            timestamp: new Date()
        });

        this.addMessage(message, 'user');
        this.showTypingIndicator();

        try {
            // إضافة تأخير واقعي للكتابة
            await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

            const response = this.generateAIResponse(message);

            // حفظ الرد في التاريخ
            this.conversationHistory.push({
                type: 'ai',
                message: response,
                timestamp: new Date()
            });

            this.hideTypingIndicator();
            this.addMessage(response, 'ai');

            // حفظ تفضيلات المستخدم
            this.updateUserPreferences(message);

        } catch (error) {
            console.error('Error getting AI response:', error);
            this.hideTypingIndicator();
            this.addMessage('عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.', 'ai');
        }
    }

    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const avatar = document.createElement('div');
        avatar.className = 'message-avatar';
        avatar.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
        
        const content = document.createElement('div');
        content.className = 'message-content';
        
        const messageText = document.createElement('div');
        messageText.className = 'message-text';
        messageText.textContent = text;
        
        const messageTime = document.createElement('div');
        messageTime.className = 'message-time';
        messageTime.textContent = this.getCurrentTime();
        
        content.appendChild(messageText);
        content.appendChild(messageTime);
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(content);
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        this.typingIndicator.style.display = 'block';
        this.scrollToBottom();
    }

    hideTypingIndicator() {
        this.typingIndicator.style.display = 'none';
    }

    generateAIResponse(userMessage) {
        // التحقق من الرد السياقي أولاً
        const contextualResponse = this.getContextualResponse(userMessage);
        if (contextualResponse) {
            return contextualResponse;
        }

        // نظام ذكاء اصطناعي محلي متقدم بدون API
        const message = userMessage.toLowerCase().trim();

        // التحقق من المواضيع المفضلة للمستخدم
        if (this.userPreferences.favoriteTopics.length > 0) {
            for (const topic of this.userPreferences.favoriteTopics) {
                if (message.includes(topic.toLowerCase())) {
                    return `أرى أنك مهتم بموضوع ${topic}! هذا رائع، يمكنني مساعدتك أكثر في هذا المجال. ما الذي تود معرفته تحديداً؟`;
                }
            }
        }

        // قاعدة بيانات المعرفة المحلية
        const knowledgeBase = {
            // التحيات والمجاملات
            greetings: {
                patterns: ['مرحبا', 'السلام', 'أهلا', 'صباح', 'مساء', 'كيف حالك', 'كيفك', 'شلونك'],
                responses: [
                    "مرحباً بك! أهلاً وسهلاً، كيف يمكنني مساعدتك اليوم؟",
                    "السلام عليكم ورحمة الله وبركاته! أهلاً بك، كيف حالك؟",
                    "صباح الخير! يسعدني أن أتحدث معك، كيف يمكنني أن أكون مفيداً؟",
                    "مساء الخير! أهلاً وسهلاً، ما الذي تود معرفته؟"
                ]
            },

            // الأسئلة الشخصية
            personal: {
                patterns: ['اسمك', 'من أنت', 'ما اسمك', 'تعريف', 'هويتك'],
                responses: [
                    "أنا مساعد ذكي تم تطويري لمساعدتك في الإجابة على أسئلتك وتقديم المعلومات المفيدة.",
                    "اسمي المساعد الذكي، وأنا هنا لمساعدتك في أي شيء تحتاجه!",
                    "أنا برنامج ذكي مصمم للمحادثة ومساعدتك في الحصول على المعلومات."
                ]
            },

            // المساعدة والدعم
            help: {
                patterns: ['مساعدة', 'ساعدني', 'أحتاج', 'كيف', 'ماذا', 'متى', 'أين', 'لماذا'],
                responses: [
                    "بالطبع! أنا هنا لمساعدتك. يمكنك أن تسألني عن أي موضوع تريد معرفة المزيد عنه.",
                    "سأكون سعيداً لمساعدتك! ما الذي تود معرفته تحديداً؟",
                    "أنا مستعد لمساعدتك في أي سؤال. اسأل عما تريد!"
                ]
            },

            // التكنولوجيا والبرمجة
            technology: {
                patterns: ['برمجة', 'كود', 'html', 'css', 'javascript', 'python', 'تطوير', 'موقع', 'تطبيق'],
                responses: [
                    "التكنولوجيا مجال رائع! يمكنني مساعدتك في فهم أساسيات البرمجة وتطوير المواقع.",
                    "البرمجة فن وعلم في نفس الوقت. ما الذي تود تعلمه تحديداً؟",
                    "تطوير المواقع والتطبيقات مهارة مهمة جداً في عصرنا الحالي. كيف يمكنني مساعدتك؟"
                ]
            },

            // التعليم والدراسة
            education: {
                patterns: ['دراسة', 'تعلم', 'مدرسة', 'جامعة', 'امتحان', 'واجب', 'بحث'],
                responses: [
                    "التعليم أساس التقدم! ما المادة أو الموضوع الذي تدرسه؟",
                    "التعلم رحلة ممتعة. كيف يمكنني مساعدتك في دراستك؟",
                    "الدراسة تحتاج إلى تنظيم وصبر. ما التحدي الذي تواجهه؟"
                ]
            },

            // الصحة والرياضة
            health: {
                patterns: ['صحة', 'رياضة', 'تمرين', 'طعام', 'نوم', 'مرض', 'علاج'],
                responses: [
                    "الصحة أهم من كل شيء! كيف يمكنني مساعدتك في الحفاظ على صحتك؟",
                    "الرياضة والغذاء الصحي أساس الحياة السليمة. ما الذي تود معرفته؟",
                    "العقل السليم في الجسم السليم. دعني أساعدك في موضوع الصحة."
                ]
            },

            // الشكر والوداع
            thanks: {
                patterns: ['شكرا', 'شكراً', 'مشكور', 'وداعا', 'مع السلامة', 'باي'],
                responses: [
                    "العفو! كان من دواعي سروري مساعدتك. أتمنى لك يوماً سعيداً!",
                    "لا شكر على واجب! أنا هنا دائماً إذا احتجت أي مساعدة.",
                    "وداعاً! أتمنى أن أكون قد ساعدتك. عد إلي في أي وقت!"
                ]
            }
        };

        // البحث عن أفضل إجابة
        let bestMatch = null;
        let highestScore = 0;

        for (const [category, data] of Object.entries(knowledgeBase)) {
            for (const pattern of data.patterns) {
                if (message.includes(pattern)) {
                    const score = this.calculateMatchScore(message, pattern);
                    if (score > highestScore) {
                        highestScore = score;
                        bestMatch = data.responses;
                    }
                }
            }
        }

        // إذا وُجدت إجابة مطابقة
        if (bestMatch) {
            const randomResponse = bestMatch[Math.floor(Math.random() * bestMatch.length)];
            return this.enhanceResponse(randomResponse, userMessage);
        }

        // إجابات ذكية للأسئلة العامة
        const smartResponses = this.generateSmartResponse(userMessage);
        if (smartResponses) {
            return smartResponses;
        }

        // إجابات افتراضية ذكية
        const defaultResponses = [
            "هذا سؤال مثير للاهتمام! دعني أفكر فيه...",
            "أفهم ما تقصده. هذا موضوع يحتاج إلى تفكير عميق.",
            "سؤال رائع! يمكنني مساعدتك في استكشاف هذا الموضوع أكثر.",
            "هذا موضوع مهم جداً. ما رأيك أن نناقشه بالتفصيل؟",
            "أقدر فضولك حول هذا الموضوع. دعني أشاركك ما أعرفه.",
            "سؤالك يدل على تفكير عميق. كيف يمكنني مساعدتك أكثر؟"
        ];

        const randomResponse = defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
        return this.enhanceResponse(randomResponse, userMessage);
    }

    calculateMatchScore(message, pattern) {
        // حساب درجة التطابق بناءً على عدة عوامل
        let score = 0;

        // التطابق المباشر
        if (message.includes(pattern)) {
            score += pattern.length;
        }

        // التطابق في بداية الجملة
        if (message.startsWith(pattern)) {
            score += 10;
        }

        // التطابق الكامل للكلمة
        const words = message.split(' ');
        if (words.includes(pattern)) {
            score += 15;
        }

        return score;
    }

    enhanceResponse(response, originalMessage) {
        // تحسين الإجابة بناءً على السياق
        const enhancements = [
            " هل تريد معرفة المزيد حول هذا الموضوع؟",
            " يمكنني مساعدتك في جوانب أخرى إذا كنت تريد.",
            " ما رأيك في هذا؟ هل لديك أسئلة أخرى؟",
            " أتمنى أن تكون هذه المعلومة مفيدة لك!",
            " إذا كان لديك أي استفسار آخر، فأنا هنا لمساعدتك."
        ];

        const randomEnhancement = enhancements[Math.floor(Math.random() * enhancements.length)];
        return response + randomEnhancement;
    }

    generateSmartResponse(message) {
        // إجابات ذكية للأسئلة الشائعة
        const smartPatterns = {
            'ما هو': 'هذا سؤال ممتاز! الموضوع الذي تسأل عنه مهم جداً ويحتاج إلى شرح مفصل.',
            'كيف أتعلم': 'التعلم رحلة رائعة! أنصحك بالبدء بالأساسيات والممارسة المستمرة.',
            'أفضل طريقة': 'هناك عدة طرق فعالة، والأفضل يعتمد على ظروفك الشخصية وأهدافك.',
            'نصيحة': 'بناءً على خبرتي، أنصحك بالتفكير بعمق في هذا الأمر واتخاذ قرار مدروس.',
            'رأيك': 'من وجهة نظري، هذا موضوع يستحق النقاش والتفكير العميق.',
            'مشكلة': 'أفهم أن هذا قد يكون محبطاً. دعنا نفكر في حلول عملية لهذه المشكلة.',
            'صعب': 'أعلم أن الأمر قد يبدو صعباً، لكن مع الصبر والمثابرة يمكن تجاوز أي تحدي.'
        };

        for (const [pattern, response] of Object.entries(smartPatterns)) {
            if (message.includes(pattern)) {
                return response + ' كيف يمكنني مساعدتك أكثر في هذا الأمر؟';
            }
        }

        return null;
    }

    loadUserPreferences() {
        // تحميل تفضيلات المستخدم من التخزين المحلي
        const saved = localStorage.getItem('chatbot_preferences');
        return saved ? JSON.parse(saved) : {
            favoriteTopics: [],
            commonQuestions: [],
            responseStyle: 'friendly',
            lastVisit: null
        };
    }

    updateUserPreferences(message) {
        // تحديث تفضيلات المستخدم بناءً على الرسائل
        const lowerMessage = message.toLowerCase();

        // تحديد المواضيع المفضلة
        const topics = ['برمجة', 'تكنولوجيا', 'صحة', 'رياضة', 'تعليم', 'طبخ', 'سفر'];
        topics.forEach(topic => {
            if (lowerMessage.includes(topic)) {
                if (!this.userPreferences.favoriteTopics.includes(topic)) {
                    this.userPreferences.favoriteTopics.push(topic);
                }
            }
        });

        // حفظ الأسئلة الشائعة
        if (message.includes('؟')) {
            this.userPreferences.commonQuestions.push(message);
            // الاحتفاظ بآخر 10 أسئلة فقط
            if (this.userPreferences.commonQuestions.length > 10) {
                this.userPreferences.commonQuestions.shift();
            }
        }

        // حفظ التفضيلات
        this.userPreferences.lastVisit = new Date();
        localStorage.setItem('chatbot_preferences', JSON.stringify(this.userPreferences));
    }

    getContextualResponse(message) {
        // إنشاء رد يعتمد على سياق المحادثة السابقة
        const recentMessages = this.conversationHistory.slice(-6); // آخر 6 رسائل

        // تحليل المزاج العام للمحادثة
        let mood = 'neutral';
        const positiveWords = ['شكرا', 'ممتاز', 'رائع', 'جيد', 'أحب'];
        const negativeWords = ['مشكلة', 'صعب', 'لا أفهم', 'محبط'];

        const allText = recentMessages.map(m => m.message).join(' ').toLowerCase();

        if (positiveWords.some(word => allText.includes(word))) {
            mood = 'positive';
        } else if (negativeWords.some(word => allText.includes(word))) {
            mood = 'negative';
        }

        // تخصيص الرد بناءً على المزاج
        if (mood === 'positive') {
            return "أسعدني أن أراك متفائلاً! " + this.generateAIResponse(message);
        } else if (mood === 'negative') {
            return "أفهم أنك قد تواجه بعض التحديات. دعني أساعدك. " + this.generateAIResponse(message);
        }

        // تحقق من التكرار
        const userMessages = recentMessages.filter(m => m.type === 'user');
        if (userMessages.length >= 2) {
            const lastMessage = userMessages[userMessages.length - 1].message;
            const secondLastMessage = userMessages[userMessages.length - 2].message;

            if (lastMessage.toLowerCase() === secondLastMessage.toLowerCase()) {
                return "لاحظت أنك كررت نفس السؤال. هل تريد توضيحاً أكثر حول هذا الموضوع؟";
            }
        }

        return null;
    }

    getCurrentTime() {
        const now = new Date();
        return now.toLocaleTimeString('ar-SA', { 
            hour: '2-digit', 
            minute: '2-digit',
            hour12: true 
        });
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    clearChat() {
        if (confirm('هل أنت متأكد من أنك تريد مسح جميع الرسائل؟')) {
            this.chatMessages.innerHTML = `
                <div class="message ai-message">
                    <div class="message-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <div class="message-text">
                            مرحباً! أنا مساعدك الذكي. كيف يمكنني مساعدتك اليوم؟
                        </div>
                        <div class="message-time">${this.getCurrentTime()}</div>
                    </div>
                </div>
            `;
        }
    }

    openChatGPT() {
        // فتح ChatGPT في نافذة جديدة
        const chatGPTWindow = window.open('https://chatgpt.com/', '_blank', 'width=1200,height=800');

        // إضافة رسالة توضيحية
        this.addMessage('تم فتح ChatGPT الحقيقي في نافذة جديدة! يمكنك استخدامه للحصول على إجابات أكثر دقة.', 'ai');

        // محاولة نقل الرسالة الأخيرة إلى الحافظة
        const lastUserMessage = this.getLastUserMessage();
        if (lastUserMessage) {
            navigator.clipboard.writeText(lastUserMessage).then(() => {
                this.addMessage('تم نسخ آخر رسالة لك إلى الحافظة. يمكنك لصقها في ChatGPT الحقيقي.', 'ai');
            }).catch(() => {
                this.addMessage('لم أتمكن من نسخ الرسالة تلقائياً. يمكنك نسخها يدوياً.', 'ai');
            });
        }
    }

    getLastUserMessage() {
        const userMessages = this.chatMessages.querySelectorAll('.user-message .message-text');
        if (userMessages.length > 0) {
            return userMessages[userMessages.length - 1].textContent;
        }
        return null;
    }

    showStatistics() {
        const stats = this.calculateStatistics();
        const statsMessage = this.formatStatistics(stats);
        this.addMessage(statsMessage, 'ai');
    }

    calculateStatistics() {
        const now = new Date();
        const sessionDuration = Math.round((now - this.sessionStartTime) / 1000 / 60); // بالدقائق

        const userMessages = this.conversationHistory.filter(m => m.type === 'user');
        const aiMessages = this.conversationHistory.filter(m => m.type === 'ai');

        // حساب متوسط طول الرسائل
        const avgUserMessageLength = userMessages.length > 0
            ? Math.round(userMessages.reduce((sum, m) => sum + m.message.length, 0) / userMessages.length)
            : 0;

        // أكثر الكلمات استخداماً
        const allUserText = userMessages.map(m => m.message).join(' ').toLowerCase();
        const words = allUserText.split(/\s+/).filter(word => word.length > 2);
        const wordCount = {};
        words.forEach(word => {
            wordCount[word] = (wordCount[word] || 0) + 1;
        });

        const topWords = Object.entries(wordCount)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 5)
            .map(([word, count]) => `${word} (${count})`);

        return {
            sessionDuration,
            totalMessages: this.conversationHistory.length,
            userMessages: userMessages.length,
            aiMessages: aiMessages.length,
            avgUserMessageLength,
            topWords,
            favoriteTopics: this.userPreferences.favoriteTopics,
            totalSessions: this.getTotalSessions()
        };
    }

    formatStatistics(stats) {
        return `📊 **إحصائيات المحادثة**

⏱️ **مدة الجلسة:** ${stats.sessionDuration} دقيقة
💬 **إجمالي الرسائل:** ${stats.totalMessages}
👤 **رسائلك:** ${stats.userMessages}
🤖 **ردودي:** ${stats.aiMessages}
📏 **متوسط طول رسائلك:** ${stats.avgUserMessageLength} حرف

🔤 **أكثر الكلمات استخداماً:**
${stats.topWords.length > 0 ? stats.topWords.join(', ') : 'لا توجد كلمات كافية'}

❤️ **مواضيعك المفضلة:**
${stats.favoriteTopics.length > 0 ? stats.favoriteTopics.join(', ') : 'لم يتم تحديد مواضيع مفضلة بعد'}

🎯 **إجمالي الجلسات:** ${stats.totalSessions}

شكراً لك على استخدام المساعد الذكي! 🌟`;
    }

    getTotalSessions() {
        const sessions = localStorage.getItem('chatbot_sessions');
        if (!sessions) {
            localStorage.setItem('chatbot_sessions', '1');
            return 1;
        }
        const count = parseInt(sessions) + 1;
        localStorage.setItem('chatbot_sessions', count.toString());
        return count;
    }

    showInitialSuggestions() {
        const initialSuggestions = [
            "مرحباً، كيف حالك؟",
            "ما اسمك؟",
            "كيف يمكنك مساعدتي؟",
            "أخبرني عن نفسك",
            "ما هي مهاراتك؟"
        ];
        this.displaySuggestions(initialSuggestions);
    }

    showSuggestions() {
        let suggestions = [];

        // اقتراحات بناءً على المواضيع المفضلة
        if (this.userPreferences.favoriteTopics.length > 0) {
            suggestions = this.userPreferences.favoriteTopics.map(topic =>
                `أخبرني المزيد عن ${topic}`
            );
        }

        // اقتراحات بناءً على سياق المحادثة
        const recentTopics = this.getRecentTopics();
        if (recentTopics.length > 0) {
            suggestions.push(...recentTopics.map(topic =>
                `ما رأيك في ${topic}؟`
            ));
        }

        // اقتراحات عامة إذا لم توجد اقتراحات أخرى
        if (suggestions.length === 0) {
            suggestions = [
                "كيف أتعلم البرمجة؟",
                "نصائح للصحة",
                "أفضل طريقة للدراسة",
                "كيف أطور مهاراتي؟",
                "اقترح عليّ موضوعاً مثيراً"
            ];
        }

        // عرض أول 4 اقتراحات فقط
        this.displaySuggestions(suggestions.slice(0, 4));
    }

    displaySuggestions(suggestions) {
        this.suggestionsList.innerHTML = '';

        suggestions.forEach(suggestion => {
            const suggestionElement = document.createElement('div');
            suggestionElement.className = 'suggestion-item';
            suggestionElement.textContent = suggestion;
            suggestionElement.addEventListener('click', () => {
                this.messageInput.value = suggestion;
                this.hideSuggestions();
                this.messageInput.focus();
                this.updateCharCount();
                this.updateSendButton();
            });
            this.suggestionsList.appendChild(suggestionElement);
        });

        this.suggestionsContainer.style.display = 'block';
    }

    hideSuggestions() {
        this.suggestionsContainer.style.display = 'none';
    }

    getRecentTopics() {
        // استخراج المواضيع من آخر 5 رسائل
        const recentMessages = this.conversationHistory
            .filter(m => m.type === 'user')
            .slice(-5)
            .map(m => m.message.toLowerCase());

        const topics = [];
        const topicKeywords = {
            'التكنولوجيا': ['برمجة', 'كمبيوتر', 'تطبيق', 'موقع'],
            'الصحة': ['رياضة', 'طعام', 'نوم', 'صحة'],
            'التعليم': ['دراسة', 'تعلم', 'مدرسة', 'جامعة'],
            'العمل': ['وظيفة', 'مهنة', 'عمل', 'شركة']
        };

        for (const [topic, keywords] of Object.entries(topicKeywords)) {
            if (keywords.some(keyword =>
                recentMessages.some(message => message.includes(keyword))
            )) {
                topics.push(topic);
            }
        }

        return topics;
    }
}

// Initialize the chatbot when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new ChatBot();
});

// Add some nice effects
document.addEventListener('DOMContentLoaded', () => {
    // Add fade-in animation to the container
    const container = document.querySelector('.container');
    container.style.opacity = '0';
    container.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        container.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
    }, 100);
});
